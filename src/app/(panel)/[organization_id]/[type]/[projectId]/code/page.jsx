"use client";

import React, { useEffect, useState, useCallback, useMemo, useContext } from "react";
import { usePara<PERSON>, useRouter, useSearchParams, usePathname } from "next/navigation";
import { Info, RefreshCcw, RefreshCw, X, Play } from 'lucide-react';
import Sessions from "@/components/Sessions/Sessions";
import { getPastCodeTasks } from "@/utils/batchAPI";
import Pagination from "@/components/UIComponents/Paginations/Pagination";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import dynamic from 'next/dynamic';
import ContainerList from "@/components/BrowsePanel/Architecture/ContainerList"
import dayjs from 'dayjs';
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useUser } from "@/components/Context/UserContext";
import { transformSessionsResponse } from "@/utils/sessionUtils";
import { resumeStartCodeMaintenance } from "@/utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
const MaintenancePage = dynamic(() => import('./maintenance/page'));
const CodeGenerationInfo = () => {
  return (
    <div className="flex items-start gap-2 bg-orange-50 rounded-md p-2 border-l-2 border-orange-400 mb-2">
      <div className="text-orange-600 flex-shrink-0 mt-0.5">
        <Info size={14} />
      </div>
      <div>
        <h2 className="text-gray-800 font-weight-medium typography-body-sm">When to use Code Generation</h2>
        <i className="text-gray-600 mt-0.5 typography-caption leading-tight">
          Generate new code components based on design specifications. Select a container and start the process
        </i>
      </div>
    </div>
  );
};

const CodePage = () => {
  const { is_having_permission } = useUser();
  const { isVisible, setIsVisible } = useCodeGeneration();
  const { projectId } = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { showAlert } = useContext(AlertContext);
  const [sessions, setSessions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('generation');
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    type: null,
    status: null,
    created_at: null
  });
  const [maintenanceActionButtons, setMaintenanceActionButtons] = useState(null);

  const fetchTasks = useCallback(async (page = 1, pageSize = 10, filters = {}) => {
    try {
      setIsLoading(true);
      const skip = (page - 1) * pageSize;

      const queryParams = new URLSearchParams();
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.type) queryParams.append('type', filters.type);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.created_at) {
        const formattedDate = dayjs(filters.created_at).format('YYYY-MM-DD');
        queryParams.append('created_at', formattedDate);
      }

      const tasks = await getPastCodeTasks(
        projectId,
        pageSize,
        skip,
        queryParams.toString()
      );

      const transformedData = transformSessionsResponse(tasks);
      setSessions(transformedData.sessions);
      setPagination(prev => ({
        ...prev,
        totalItems: transformedData.pagination.total_count
      }));
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handlePageSizeChange = (newSize) => {
    setPagination(prev => ({
      ...prev,
      pageSize: newSize,
      currentPage: 1
    }));
  };

  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  }, []);

  const handleHistoryOpen = () => {
    fetchTasks(pagination.currentPage, pagination.pageSize, filters);
    setIsHistoryOpen(true);
  };

  useEffect(() => {
    if (searchParams.get('history') === 'true') {
      handleHistoryOpen();
    }
  }, [searchParams]);

  useEffect(() => {
    if (!isHistoryOpen && searchParams.get('history') === 'true') {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('history');
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [isHistoryOpen]);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
      // Remove the tab parameter from URL after setting the active tab
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('tab');
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [searchParams, pathname, router]);

  useEffect(() => {
    fetchTasks(pagination.currentPage, pagination.pageSize, filters);
  }, [projectId, pagination.currentPage, pagination.pageSize, filters]);

  // Resume code maintenance function that matches the working Sessions pattern
  const resumecodemaintainance = useCallback(async (taskId) => {
    try {
      console.log("Resuming code maintenance for task:", taskId);
      console.log("Current pathname:", pathname);
      console.log("Current searchParams:", searchParams.toString());
      
      const selectedRepos = { all_repositories: true };
      const sessionName = "Untitled";
      const sessionDescription = "Resumed maintenance session";
      
      const response = await resumeStartCodeMaintenance(
        projectId,
        taskId,
        selectedRepos,
        sessionName,
        sessionDescription
      );

      console.log("Resume response:", response);

      console.log("Full response object:", JSON.stringify(response, null, 2));

      // Handle response - check for task_id first, regardless of other fields
      if (response && response.task_id) {
        console.log("Got task_id:", response.task_id);
        console.log("Response end flag:", response.end);
                
        // Navigate to the task_id URL immediately using multiple approaches
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set("task_id", response.task_id);
        const newUrl = currentUrl.toString();
        
        console.log("Current URL:", window.location.href);
        console.log("Target URL:", newUrl);
        
        // Method 1: Use history API first for immediate URL update
        try {
          window.history.pushState({ task_id: response.task_id }, '', newUrl);
          console.log("history.pushState() called - URL updated immediately");
        } catch (error) {
          console.error("history.pushState() failed:", error);
        }
        
        // Method 2: Use router.push for Next.js navigation
        try {
          router.push(newUrl);
          console.log("router.push() called");
        } catch (error) {
          console.error("router.push() failed:", error);
        }
        
        // Show the modal after a small delay to ensure URL is updated
        setTimeout(() => {
          console.log("Showing modal, current URL:", window.location.href);
          setIsVisible(true);
        }, 50);
        
        // Fallback navigation if URL still not updated
        setTimeout(() => {
          const currentUrlAfter = window.location.href;
          console.log("URL after 200ms:", currentUrlAfter);
          if (!currentUrlAfter.includes(`task_id=${response.task_id}`)) {
            console.warn("URL still not updated! Forcing with window.location.href");
            window.location.href = newUrl;
          }
        }, 200);
        
        return response;
      } else if (response && response.error) {
        console.log("Got error response:", response.error);
        // Handle API-level duplicate error messages
        if (response.duplicate || response.error.includes("already finished loading")) {
          showAlert(response.message || "This session may have been resumed already. Please check your active sessions.", "warning");
        } else {
          showAlert(response.error, "error");
        }
        return null;
      } else if (response && response.message && !response.task_id) {
        // Only handle messages that don't have task_id (to avoid conflicts)
        console.log("Got message-only response:", response.message);
        showAlert(response.message, response.end ? "success" : "info");
        return response;
      } else {
        console.log("Unexpected response format:", response);
        showAlert("Unexpected response format. Please try again.", "warning");
        return null;
      }
    } catch (error) {
      console.error("Failed to resume code maintenance:", error);
      
      // Handle specific duplicate request errors
      if (error.message && error.message.includes("already finished loading")) {
        showAlert("This session may have been resumed already. Please check your active sessions.", "warning");
        return null;
      } else if (error.message && error.message.includes("JSON")) {
        showAlert("Response format error. Please try again or contact support.", "error");
      } else if (error.message && (error.message.includes("network") || error.message.includes("fetch"))) {
        showAlert("Network error. Please check your connection and try again.", "error");
      } else if (error.message) {
        showAlert(error.message, "error");
      } else {
        showAlert("Failed to resume code maintenance. Please try again.", "error");
      }
      return null;
    }
  }, [projectId, searchParams, pathname, router, showAlert, setIsVisible]);

  // Make the function available globally
  useEffect(() => {
    window.resumecodemaintainance = resumecodemaintainance;
    
    return () => {
      delete window.resumecodemaintainance;
    };
  }, [resumecodemaintainance]);

  return (
    <div className="overflow-y-auto custom-scrollbar max-h-[78vh]">
      <div className="p-3">
        {/* Header Section with Tabs and Action Buttons */}
        <div className="flex items-center justify-between mb-3 h-8">
          {/* Left: Tabs */}
          <div className="bg-gray-100 h-8 flex border border-gray-200 rounded-md overflow-hidden">
            <BootstrapTooltip
              title={!is_having_permission() ? "You don't have permission" : "Start code generation"}
              placement="bottom"
            >
              <button
                onClick={() => {
                  if (is_having_permission()) {
                    setActiveTab('generation');
                  }
                }}
                className={`px-4 py-1 typography-body-sm font-weight-medium transition-all duration-200 ease-in-out whitespace-nowrap ${
                  activeTab === 'generation'
                    ? 'bg-white text-orange-600 shadow-sm border-r border-gray-200'
                    : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
                }`}
                disabled={!is_having_permission()}
              >
                Code Generation
              </button>
            </BootstrapTooltip>

            <BootstrapTooltip
              title={!is_having_permission() ? "You don't have permission" : "Start code maintenance"}
              placement="bottom"
            >
              <button
                onClick={() => {
                  if (is_having_permission()) {
                    setActiveTab('maintenance');
                  }
                }}
                className={`px-4 py-1 typography-body-sm font-weight-medium transition-all duration-200 ease-in-out whitespace-nowrap ${
                  activeTab === 'maintenance'
                    ? 'bg-white text-orange-600 shadow-sm border-l border-gray-200'
                    : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
                }`}
                disabled={!is_having_permission()}
              >
                Code Maintenance
              </button>
            </BootstrapTooltip>
          </div>

          {/* Right: Action Buttons - Show different buttons based on active tab */}
          <div className="flex items-center gap-2 h-8">
            {/* History Button - Show for both tabs */}
            <BootstrapTooltip title={TOOLTIP_CONTENT.codeMaintenance.History} placement="bottom">
              <button
                onClick={() => {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.set("history", "true");
                  router.push(`${pathname}?${newSearchParams.toString()}`);
                }}
                className="flex items-center justify-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-orange-50 hover:border-orange-300 hover:text-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
              >
                <RefreshCcw className="w-3.5 h-3.5" />
                <span className="ml-1.5">History</span>
              </button>
            </BootstrapTooltip>

            {/* Refresh Button - Show for both tabs */}
            <BootstrapTooltip title={activeTab === 'maintenance' ? "Refresh repositories list" : "Refresh page"} placement="bottom">
              <button
                onClick={() => {
                  if (activeTab === 'maintenance') {
                    // Trigger maintenance page refresh
                    const event = new CustomEvent('refreshMaintenance');
                    window.dispatchEvent(event);
                  } else {
                    window.location.reload();
                  }
                }}
                className="flex items-center justify-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-orange-50 hover:border-orange-300 hover:text-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
              >
                <RefreshCw className="w-3.5 h-3.5" />
                <span className="ml-1.5">Refresh</span>
              </button>
            </BootstrapTooltip>

            {/* Maintenance-specific buttons */}
            {activeTab === 'maintenance' && (
              <>
                {/* Deselect All / Select All Button - This will be controlled by maintenance page */}
                <button
                  id="maintenance-select-all-btn"
                  className="flex items-center px-3 py-1.5 bg-white border border-gray-300 rounded-md hover:bg-orange-50 hover:border-orange-300 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                >
                  <div className="flex items-center justify-center mr-1.5">
                    <div className="w-3.5 h-3.5 flex items-center justify-center border border-gray-400 rounded transition-colors">
                    </div>
                  </div>
                  <span className="text-gray-700">Select All</span>
                </button>

                {/* Start Session Button - This will be controlled by maintenance page */}
                <button
                  id="maintenance-start-session-btn"
                  className="flex items-center justify-center px-3 py-1.5 bg-orange-500 hover:bg-orange-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                >
                  <Play className="w-3.5 h-3.5 mr-1.5" />
                  <span>Start Session</span>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'generation' && (
          <div>
            <CodeGenerationInfo />
            <ContainerList isModal={true} />
          </div>
        )}
        {activeTab === 'maintenance' && (
          <div>
            <MaintenancePage />
          </div>
        )}
      </div>

      {/* History Modal */}
      {isHistoryOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-lg w-[94vw] h-[94vh] flex flex-col shadow-xl">
            <div className="flex-1 overflow-hidden flex flex-col">
              <div className="flex-1 overflow-y-auto custom-scrollbar p-2">
                <Sessions
                  initialSessions={sessions}
                  isLoading={isLoading}
                  onFilterChange={handleFilterChange}
                  onCloseModal={() => setIsHistoryOpen(false)}
                  compactMode={true}
                />
              </div>
              <div className="sticky bottom-0 bg-white border-t border-gray-200 py-3 px-4">
                <div className="flex items-center justify-between">
                  <div className="typography-caption text-gray-500">
                    Showing {sessions.length} of {pagination.totalItems} sessions
                  </div>
                  <Pagination
                    currentPage={pagination.currentPage}
                    pageCount={Math.max(1, Math.ceil(pagination.totalItems / pagination.pageSize))}
                    pageSize={pagination.pageSize}
                    totalItems={pagination.totalItems}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    pageSizeOptions={[5, 10, 20, 50]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {isVisible && <CodeGenerationModal />}
    </div>
  );
};

export default CodePage;
